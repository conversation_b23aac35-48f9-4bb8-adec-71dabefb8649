'use client';

import { useState, useMemo } from 'react';
import { ChevronDown, ChevronUp, Info, BookOpen, Network, Copy, Search } from 'lucide-react';
import { WordNetData } from '@/models';
import { useTranslation } from '@/contexts/translation-context';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button, LoadingSpinner } from '@/components/ui';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface WordNetInfoProps {
	wordNetData: WordNetData;
	term: string;
	className?: string;
	onSearchTerm?: (term: string) => void;
	searchLoading?: Record<string, boolean>;
}

interface WordNetSectionProps {
	title: string;
	items: string[];
	emptyMessage: string;
	tooltip: string;
	icon: React.ReactNode;
	maxInitialItems?: number;
	onSearchTerm?: (term: string) => void;
	searchLoading?: Record<string, boolean>;
}

function WordNetSection({
	title,
	items,
	emptyMessage,
	tooltip,
	icon,
	maxInitialItems = 3,
	onSearchTerm,
	searchLoading = {},
}: WordNetSectionProps) {
	const [isExpanded, setIsExpanded] = useState(false);
	const { t } = useTranslation();

	// Memoize loading states for each item to ensure re-renders when loading changes
	const itemLoadingStates = useMemo(() => {
		return items.reduce((acc, item) => {
			acc[item] = Boolean(searchLoading[item]);
			return acc;
		}, {} as Record<string, boolean>);
	}, [items, searchLoading]);

	if (items.length === 0) {
		return (
			<div className="space-y-2">
				<div className="flex items-center gap-2">
					{icon}
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<span className="text-sm font-medium text-muted-foreground cursor-help">
									{title}
									<Info className="inline w-3 h-3 ml-1" />
								</span>
							</TooltipTrigger>
							<TooltipContent>
								<p className="max-w-xs">{tooltip}</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<p className="text-xs text-muted-foreground italic">{emptyMessage}</p>
			</div>
		);
	}

	const displayItems = isExpanded ? items : items.slice(0, maxInitialItems);
	const hasMore = items.length > maxInitialItems;

	return (
		<div className="space-y-2">
			<div className="flex items-center gap-2">
				{icon}
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<span className="text-sm font-medium cursor-help">
								{title}
								<Info className="inline w-3 h-3 ml-1" />
							</span>
						</TooltipTrigger>
						<TooltipContent>
							<p className="max-w-xs">{tooltip}</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
				<Badge variant="secondary" className="text-xs">
					{items.length}
				</Badge>
			</div>

			<div className="space-y-1">
				{displayItems.map((item, index) => {
					const isLoading = itemLoadingStates[item];
					return (
						<div
							key={index}
							className="flex items-center justify-between p-2 bg-muted/50 rounded-md text-sm group hover:bg-muted/70 transition-colors"
						>
							<span className="flex-1">{item}</span>
							<div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
								<Button
									variant="ghost"
									size="sm"
									className="h-6 w-6 p-0"
									onClick={() => navigator.clipboard.writeText(item)}
									title={t('wordnet.copyTerm')}
								>
									<Copy className="w-3 h-3" />
								</Button>
								<Button
									variant="ghost"
									size="sm"
									className="h-6 w-6 p-0"
									onClick={() => {
										onSearchTerm?.(item);
									}}
									title={t('wordnet.searchTerm')}
									disabled={isLoading}
									loading={isLoading}
								>
									<Search className="w-3 h-3" />
								</Button>
							</div>
						</div>
					);
				})}
			</div>

			{hasMore && (
				<Button
					variant="ghost"
					size="sm"
					onClick={() => setIsExpanded(!isExpanded)}
					className="w-full text-xs"
				>
					{isExpanded ? (
						<>
							<ChevronUp className="w-3 h-3 mr-1" />
							{t('wordnet.showLess')}
						</>
					) : (
						<>
							<ChevronDown className="w-3 h-3 mr-1" />
							{t('wordnet.showMore')} ({items.length - maxInitialItems} more)
						</>
					)}
				</Button>
			)}
		</div>
	);
}

export function WordNetInfo({
	wordNetData,
	term,
	className,
	onSearchTerm,
	searchLoading = {},
}: WordNetInfoProps) {
	const { t } = useTranslation();
	const [isCollapsed, setIsCollapsed] = useState(false);

	// Check if we have any meaningful data
	const hasData =
		wordNetData.synsets.length > 0 ||
		wordNetData.hypernyms.length > 0 ||
		wordNetData.hyponyms.length > 0 ||
		wordNetData.holonyms.length > 0 ||
		wordNetData.meronyms.length > 0 ||
		wordNetData.lemma;

	if (!hasData) {
		return (
			<Card className={className}>
				<CardHeader className="pb-3">
					<CardTitle className="text-sm flex items-center gap-2">
						<Network className="w-4 h-4" />
						{t('wordnet.title')}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground italic">{t('wordnet.noData')}</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className={className}>
			<Collapsible open={!isCollapsed} onOpenChange={setIsCollapsed}>
				<CardHeader className="pb-3">
					<CollapsibleTrigger asChild>
						<div className="flex items-center justify-between cursor-pointer">
							<CardTitle className="text-sm flex items-center gap-2">
								<Network className="w-4 h-4" />
								{t('wordnet.title')}
							</CardTitle>
							<Button variant="ghost" size="sm" className="h-6 w-6 p-0">
								{isCollapsed ? (
									<ChevronDown className="w-4 h-4" />
								) : (
									<ChevronUp className="w-4 h-4" />
								)}
							</Button>
						</div>
					</CollapsibleTrigger>
				</CardHeader>

				<CollapsibleContent>
					<CardContent className="space-y-4">
						{/* Lemma */}
						{wordNetData.lemma && (
							<div className="space-y-2">
								<div className="flex items-center gap-2">
									<BookOpen className="w-4 h-4" />
									<TooltipProvider>
										<Tooltip>
											<TooltipTrigger asChild>
												<span className="text-sm font-medium cursor-help">
													{t('wordnet.lemma')}
													<Info className="inline w-3 h-3 ml-1" />
												</span>
											</TooltipTrigger>
											<TooltipContent>
												<p className="max-w-xs">
													{t('wordnet.tooltips.lemma')}
												</p>
											</TooltipContent>
										</Tooltip>
									</TooltipProvider>
								</div>
								<Badge variant="outline" className="font-mono">
									{wordNetData.lemma}
								</Badge>
							</div>
						)}

						{/* Synsets */}
						<WordNetSection
							title={t('wordnet.synsets')}
							items={wordNetData.synsets}
							emptyMessage={t('wordnet.empty.synsets')}
							tooltip={t('wordnet.tooltips.synsets')}
							icon={<BookOpen className="w-4 h-4" />}
							maxInitialItems={2}
							onSearchTerm={onSearchTerm}
							searchLoading={searchLoading}
						/>

						{/* Hypernyms */}
						<WordNetSection
							title={t('wordnet.hypernyms')}
							items={wordNetData.hypernyms}
							emptyMessage={t('wordnet.empty.hypernyms')}
							tooltip={t('wordnet.tooltips.hypernyms')}
							icon={<ChevronUp className="w-4 h-4" />}
							onSearchTerm={onSearchTerm}
							searchLoading={searchLoading}
						/>

						{/* Hyponyms */}
						<WordNetSection
							title={t('wordnet.hyponyms')}
							items={wordNetData.hyponyms}
							emptyMessage={t('wordnet.empty.hyponyms')}
							tooltip={t('wordnet.tooltips.hyponyms')}
							icon={<ChevronDown className="w-4 h-4" />}
							onSearchTerm={onSearchTerm}
							searchLoading={searchLoading}
						/>

						{/* Holonyms */}
						<WordNetSection
							title={t('wordnet.holonyms')}
							items={wordNetData.holonyms}
							emptyMessage={t('wordnet.empty.holonyms')}
							tooltip={t('wordnet.tooltips.holonyms')}
							icon={<Network className="w-4 h-4" />}
							onSearchTerm={onSearchTerm}
							searchLoading={searchLoading}
						/>

						{/* Meronyms */}
						<WordNetSection
							title={t('wordnet.meronyms')}
							items={wordNetData.meronyms}
							emptyMessage={t('wordnet.empty.meronyms')}
							tooltip={t('wordnet.tooltips.meronyms')}
							icon={<Network className="w-4 h-4" />}
							onSearchTerm={onSearchTerm}
							searchLoading={searchLoading}
						/>

						{/* Source attribution */}
						<div className="pt-2 border-t">
							<p className="text-xs text-muted-foreground">
								{t('wordnet.poweredBy')}
							</p>
						</div>
					</CardContent>
				</CollapsibleContent>
			</Collapsible>
		</Card>
	);
}
